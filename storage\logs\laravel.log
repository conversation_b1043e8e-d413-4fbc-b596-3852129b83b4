[2025-08-16 08:55:31] local.ERROR: There are no commands defined in the "generate" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"generate\" namespace. at C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('generate')
#1 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('generate:key')
#2 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-08-16 09:00:01] local.ERROR: The [pcntl] extension is required to run Pail. {"exception":"[object] (RuntimeException(code: 0): The [pcntl] extension is required to run Pail. at C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\pail\\src\\Guards\\EnsurePcntlIsAvailable.php:15)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\pail\\src\\Console\\Commands\\PailCommand.php(46): Laravel\\Pail\\Guards\\EnsurePcntlIsAvailable::check()
#1 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Pail\\Console\\Commands\\PailCommand->handle(Object(Laravel\\Pail\\ProcessFactory))
#2 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#7 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#9 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Pail\\Console\\Commands\\PailCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#15 {main}
"} 
[2025-08-16 09:01:45] local.ERROR: The [pcntl] extension is required to run Pail. {"exception":"[object] (RuntimeException(code: 0): The [pcntl] extension is required to run Pail. at C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\pail\\src\\Guards\\EnsurePcntlIsAvailable.php:15)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\pail\\src\\Console\\Commands\\PailCommand.php(46): Laravel\\Pail\\Guards\\EnsurePcntlIsAvailable::check()
#1 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Pail\\Console\\Commands\\PailCommand->handle(Object(Laravel\\Pail\\ProcessFactory))
#2 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#7 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#9 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Pail\\Console\\Commands\\PailCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#15 {main}
"} 
[2025-08-16 09:16:39] local.ERROR: The [pcntl] extension is required to run Pail. {"exception":"[object] (RuntimeException(code: 0): The [pcntl] extension is required to run Pail. at C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\pail\\src\\Guards\\EnsurePcntlIsAvailable.php:15)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\pail\\src\\Console\\Commands\\PailCommand.php(46): Laravel\\Pail\\Guards\\EnsurePcntlIsAvailable::check()
#1 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Pail\\Console\\Commands\\PailCommand->handle(Object(Laravel\\Pail\\ProcessFactory))
#2 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#7 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#9 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Pail\\Console\\Commands\\PailCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#15 {main}
"} 
[2025-08-16 09:20:26] local.ERROR: The [pcntl] extension is required to run Pail. {"exception":"[object] (RuntimeException(code: 0): The [pcntl] extension is required to run Pail. at C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\pail\\src\\Guards\\EnsurePcntlIsAvailable.php:15)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\pail\\src\\Console\\Commands\\PailCommand.php(46): Laravel\\Pail\\Guards\\EnsurePcntlIsAvailable::check()
#1 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Pail\\Console\\Commands\\PailCommand->handle(Object(Laravel\\Pail\\ProcessFactory))
#2 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#7 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#9 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Pail\\Console\\Commands\\PailCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#15 {main}
"} 
[2025-08-16 09:24:31] local.ERROR: The [pcntl] extension is required to run Pail. {"exception":"[object] (RuntimeException(code: 0): The [pcntl] extension is required to run Pail. at C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\pail\\src\\Guards\\EnsurePcntlIsAvailable.php:15)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\pail\\src\\Console\\Commands\\PailCommand.php(46): Laravel\\Pail\\Guards\\EnsurePcntlIsAvailable::check()
#1 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Pail\\Console\\Commands\\PailCommand->handle(Object(Laravel\\Pail\\ProcessFactory))
#2 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#6 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#7 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#8 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#9 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Pail\\Console\\Commands\\PailCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#12 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#15 {main}
"} 
[2025-08-16 09:34:09] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: permissions.module (Connection: sqlite, SQL: insert into "permissions" ("guard_name", "name", "updated_at", "created_at") values (web, view articles, 2025-08-16 09:34:09, 2025-08-16 09:34:09)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: permissions.module (Connection: sqlite, SQL: insert into \"permissions\" (\"guard_name\", \"name\", \"updated_at\", \"created_at\") values (web, view articles, 2025-08-16 09:34:09, 2025-08-16 09:34:09)) at C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"pe...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"pe...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"pe...', Array)
#3 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"pe...', Array)
#4 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3769): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"pe...', Array, 'id')
#5 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2143): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#6 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1361): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1326): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1165): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1127): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(Spatie\\Permission\\Models\\Permission))
#11 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1126): tap(Object(Spatie\\Permission\\Models\\Permission), Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php(52): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\database\\seeders\\RolesAndPermissionsSeeder.php(22): Spatie\\Permission\\Models\\Permission::create(Array)
#14 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\RolesAndPermissionsSeeder->run()
#15 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#20 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#21 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke(Array)
#22 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\database\\seeders\\DatabaseSeeder.php(16): Illuminate\\Database\\Seeder->call('Database\\\\Seeder...')
#23 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#24 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#25 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#28 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#29 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#30 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(69): Illuminate\\Database\\Seeder->__invoke()
#31 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#32 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(68): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#34 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#38 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#39 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#41 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#47 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: permissions.module at C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"pe...', Array)
#2 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"pe...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"pe...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"pe...', Array)
#5 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"pe...', Array)
#6 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3769): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"pe...', Array, 'id')
#7 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2143): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#8 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1361): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#9 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1326): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#10 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1165): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#11 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1127): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(Spatie\\Permission\\Models\\Permission))
#13 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1126): tap(Object(Spatie\\Permission\\Models\\Permission), Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php(52): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#15 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\database\\seeders\\RolesAndPermissionsSeeder.php(22): Spatie\\Permission\\Models\\Permission::create(Array)
#16 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\RolesAndPermissionsSeeder->run()
#17 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#22 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#23 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke(Array)
#24 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\database\\seeders\\DatabaseSeeder.php(16): Illuminate\\Database\\Seeder->call('Database\\\\Seeder...')
#25 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#26 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#27 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#30 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#31 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#32 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(69): Illuminate\\Database\\Seeder->__invoke()
#33 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#34 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(68): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#35 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#36 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#37 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#40 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#41 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#42 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#49 {main}
"} 
[2025-08-16 09:35:27] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: permissions.module (Connection: sqlite, SQL: insert into "permissions" ("guard_name", "name", "updated_at", "created_at") values (web, view articles, 2025-08-16 09:35:27, 2025-08-16 09:35:27)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: permissions.module (Connection: sqlite, SQL: insert into \"permissions\" (\"guard_name\", \"name\", \"updated_at\", \"created_at\") values (web, view articles, 2025-08-16 09:35:27, 2025-08-16 09:35:27)) at C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"pe...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"pe...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"pe...', Array)
#3 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"pe...', Array)
#4 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3769): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"pe...', Array, 'id')
#5 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2143): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#6 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1361): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1326): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1165): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1127): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(Spatie\\Permission\\Models\\Permission))
#11 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1126): tap(Object(Spatie\\Permission\\Models\\Permission), Object(Closure))
#12 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php(52): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\database\\seeders\\RolesAndPermissionsSeeder.php(22): Spatie\\Permission\\Models\\Permission::create(Array)
#14 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\RolesAndPermissionsSeeder->run()
#15 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#16 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#17 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#18 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#19 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#20 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#21 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke(Array)
#22 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\database\\seeders\\DatabaseSeeder.php(16): Illuminate\\Database\\Seeder->call('Database\\\\Seeder...')
#23 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#24 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#25 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#26 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#28 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#29 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#30 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(69): Illuminate\\Database\\Seeder->__invoke()
#31 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#32 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(68): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#33 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#34 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#38 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#39 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#41 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#47 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: permissions.module at C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"pe...', Array)
#2 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"pe...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"pe...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"pe...', Array)
#5 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"pe...', Array)
#6 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3769): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"pe...', Array, 'id')
#7 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2143): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#8 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1361): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#9 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1326): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#10 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1165): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#11 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1127): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(Spatie\\Permission\\Models\\Permission))
#13 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1126): tap(Object(Spatie\\Permission\\Models\\Permission), Object(Closure))
#14 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php(52): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#15 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\database\\seeders\\RolesAndPermissionsSeeder.php(22): Spatie\\Permission\\Models\\Permission::create(Array)
#16 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\RolesAndPermissionsSeeder->run()
#17 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#22 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#23 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke(Array)
#24 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\database\\seeders\\DatabaseSeeder.php(16): Illuminate\\Database\\Seeder->call('Database\\\\Seeder...')
#25 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#26 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#27 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#30 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#31 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#32 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(69): Illuminate\\Database\\Seeder->__invoke()
#33 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#34 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(68): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#35 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#36 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#37 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#40 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#41 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#42 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#47 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#48 C:\\Users\\<USER>\\Documents\\dev\\laravel12-react-roles-permissions-main\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#49 {main}
"} 
