<?php

use App\Http\Controllers\CategoryController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    //Route::resource('products', ProductController::class)->middleware('permission:access-products-module');
    //Route::resource('categories', CategoryController::class)->middleware('permission:access-categories-module');
    //Route::resource('permissions', PermissionController::class)->middleware('permission:access-permissions-module');
    //Route::resource('roles', RoleController::class)->middleware('permission:access-roles-module');
    //Route::resource('users', UserController::class)->middleware('permission:access-users-module');

    Route::resource('products', ProductController::class);
    Route::resource('categories', CategoryController::class);
    Route::resource('permissions', PermissionController::class);
    Route::resource('roles', RoleController::class);
    Route::resource('users', UserController::class);

});

require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';
