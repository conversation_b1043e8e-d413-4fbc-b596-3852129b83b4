<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create Permissions
        Permission::create(['name' => 'view articles']);
        Permission::create(['name' => 'edit articles']);
        Permission::create(['name' => 'delete articles']);

        // Create Roles and Assign Permissions
        $adminRole = Role::create(['name' => 'admin']);
        $adminRole->givePermissionTo(['view articles', 'edit articles', 'delete articles']);

        $editorRole = Role::create(['name' => 'editor']);
        $editorRole->givePermissionTo(['view articles', 'edit articles']);

        $userRole = Role::create(['name' => 'user']);
        $userRole->givePermissionTo(['view articles']);

        // Assign Roles to Users (Optional, if you have existing users)
        // Example: Assign 'admin' role to a user with ID 1
        // $user = \App\Models\User::find(1);
        // if ($user) {
        //     $user->assignRole('admin');
        // }
    }
}