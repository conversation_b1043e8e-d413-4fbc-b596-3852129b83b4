<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Role;
use App\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create Permissions with required module field
        $permissions = [
            // User Management Permissions
            [
                'module' => 'users',
                'name' => 'view users',
                'label' => 'View Users',
                'description' => 'Can view users list and details',
                'guard_name' => 'web'
            ],
            [
                'module' => 'users',
                'name' => 'create users',
                'label' => 'Create Users',
                'description' => 'Can create new users',
                'guard_name' => 'web'
            ],
            [
                'module' => 'users',
                'name' => 'edit users',
                'label' => 'Edit Users',
                'description' => 'Can edit existing users',
                'guard_name' => 'web'
            ],
            [
                'module' => 'users',
                'name' => 'delete users',
                'label' => 'Delete Users',
                'description' => 'Can delete users',
                'guard_name' => 'web'
            ],

            // Role Management Permissions
            [
                'module' => 'roles',
                'name' => 'view roles',
                'label' => 'View Roles',
                'description' => 'Can view roles list and details',
                'guard_name' => 'web'
            ],
            [
                'module' => 'roles',
                'name' => 'create roles',
                'label' => 'Create Roles',
                'description' => 'Can create new roles',
                'guard_name' => 'web'
            ],
            [
                'module' => 'roles',
                'name' => 'edit roles',
                'label' => 'Edit Roles',
                'description' => 'Can edit existing roles',
                'guard_name' => 'web'
            ],
            [
                'module' => 'roles',
                'name' => 'delete roles',
                'label' => 'Delete Roles',
                'description' => 'Can delete roles',
                'guard_name' => 'web'
            ],

            // Permission Management Permissions
            [
                'module' => 'permissions',
                'name' => 'view permissions',
                'label' => 'View Permissions',
                'description' => 'Can view permissions list and details',
                'guard_name' => 'web'
            ],
            [
                'module' => 'permissions',
                'name' => 'create permissions',
                'label' => 'Create Permissions',
                'description' => 'Can create new permissions',
                'guard_name' => 'web'
            ],
            [
                'module' => 'permissions',
                'name' => 'edit permissions',
                'label' => 'Edit Permissions',
                'description' => 'Can edit existing permissions',
                'guard_name' => 'web'
            ],
            [
                'module' => 'permissions',
                'name' => 'delete permissions',
                'label' => 'Delete Permissions',
                'description' => 'Can delete permissions',
                'guard_name' => 'web'
            ],

            // Article Management Permissions
            [
                'module' => 'articles',
                'name' => 'view articles',
                'label' => 'View Articles',
                'description' => 'Can view articles list and details',
                'guard_name' => 'web'
            ],
            [
                'module' => 'articles',
                'name' => 'create articles',
                'label' => 'Create Articles',
                'description' => 'Can create new articles',
                'guard_name' => 'web'
            ],
            [
                'module' => 'articles',
                'name' => 'edit articles',
                'label' => 'Edit Articles',
                'description' => 'Can edit existing articles',
                'guard_name' => 'web'
            ],
            [
                'module' => 'articles',
                'name' => 'delete articles',
                'label' => 'Delete Articles',
                'description' => 'Can delete articles',
                'guard_name' => 'web'
            ],

            // Product Management Permissions (based on ProductFactory found)
            [
                'module' => 'products',
                'name' => 'view products',
                'label' => 'View Products',
                'description' => 'Can view products list and details',
                'guard_name' => 'web'
            ],
            [
                'module' => 'products',
                'name' => 'create products',
                'label' => 'Create Products',
                'description' => 'Can create new products',
                'guard_name' => 'web'
            ],
            [
                'module' => 'products',
                'name' => 'edit products',
                'label' => 'Edit Products',
                'description' => 'Can edit existing products',
                'guard_name' => 'web'
            ],
            [
                'module' => 'products',
                'name' => 'delete products',
                'label' => 'Delete Products',
                'description' => 'Can delete products',
                'guard_name' => 'web'
            ],
        ];

        // Create permissions
        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name'], 'guard_name' => $permission['guard_name']],
                $permission
            );
        }

        // Create Roles and Assign Permissions
        $superAdminRole = Role::firstOrCreate(
            ['name' => 'super-admin', 'guard_name' => 'web'],
            ['name' => 'super-admin', 'guard_name' => 'web']
        );
        // Super Admin gets all permissions
        $superAdminRole->syncPermissions(Permission::all());

        $adminRole = Role::firstOrCreate(
            ['name' => 'admin', 'guard_name' => 'web'],
            ['name' => 'admin', 'guard_name' => 'web']
        );
        // Admin gets most permissions except super admin level permissions
        $adminPermissions = [
            'view users', 'create users', 'edit users',
            'view roles', 'view permissions',
            'view articles', 'create articles', 'edit articles', 'delete articles',
            'view products', 'create products', 'edit products', 'delete products'
        ];
        $adminRole->syncPermissions($adminPermissions);

        $editorRole = Role::firstOrCreate(
            ['name' => 'editor', 'guard_name' => 'web'],
            ['name' => 'editor', 'guard_name' => 'web']
        );
        // Editor can manage articles and products
        $editorPermissions = [
            'view articles', 'create articles', 'edit articles',
            'view products', 'create products', 'edit products'
        ];
        $editorRole->syncPermissions($editorPermissions);

        $userRole = Role::firstOrCreate(
            ['name' => 'user', 'guard_name' => 'web'],
            ['name' => 'user', 'guard_name' => 'web']
        );
        // Regular user can only view
        $userPermissions = [
            'view articles',
            'view products'
        ];
        $userRole->syncPermissions($userPermissions);

        $this->command->info('Roles and permissions created successfully!');
        $this->command->info('Created <img src="" alt="" sizes="" srcset="">oles: super-admin, admin, editor, user');
        $this->command->info('Created ' . count($permissions) . ' permissions across multiple modules');
    }
}